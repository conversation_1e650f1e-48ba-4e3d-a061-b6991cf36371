class TTSManager {
    constructor() {
        this.isEnabled = true;
        this.volume = 0.8;
        this.rate = 0.7; // Tốc độ đọc chậm cho trẻ em
        this.pitch = 1.2; // <PERSON> hơn một chút
        
        // <PERSON>h sách giọng nữ tiếng Việt
        this.vietnameseVoices = [
            'Vietnamese Female', // ResponsiveVoice
            'vi-VN-Standard-A', // Google
            'vi-VN-Wavenet-A', // Google Wavenet
            'Microsoft Linh - Vietnamese (Vietnam)', // Microsoft
            'Vietnamese (Vietnam)'
        ];
        
        this.currentVoice = null;
        this.initializeVoice();
        
        // Fallback messages nếu không có internet
        this.fallbackMessages = {
            correct: [
                'Tuyệt vời! Bé làm đúng rồi!',
                'Giỏi quá! Bé thật thông minh!',
                'Chính xác! Bé làm rất tốt!',
                'Xuất sắc! Bé là nhà toán học nhí!'
            ],
            incorrect: [
                'Chưa đúng! Bé sẽ làm được thôi!',
                '<PERSON> rồi bé ơi! Lần sau bé sẽ làm được!',
                'Chưa chính xác! Bé cố gắng lên!',
                'Gần đúng rồi! Bé thử lại nhé!'
            ]
        };
    }
    
    initializeVoice() {
        // Đợi ResponsiveVoice load xong
        if (typeof responsiveVoice !== 'undefined') {
            this.setupResponsiveVoice();
        } else {
            // Fallback về Web Speech API
            this.setupWebSpeechAPI();
        }
    }
    
    setupResponsiveVoice() {
        console.log('Sử dụng ResponsiveVoice API');
        this.ttsEngine = 'responsivevoice';
        
        // Kiểm tra giọng nữ tiếng Việt có sẵn
        responsiveVoice.OnVoiceReady = () => {
            const voices = responsiveVoice.getVoices();
            console.log('Danh sách giọng có sẵn:', voices);
            
            // Tìm giọng nữ tiếng Việt
            const vietnameseVoice = voices.find(voice => 
                voice.name.toLowerCase().includes('vietnamese') && 
                voice.name.toLowerCase().includes('female')
            );
            
            if (vietnameseVoice) {
                this.currentVoice = vietnameseVoice.name;
                console.log('Đã chọn giọng:', this.currentVoice);
            } else {
                // Fallback về giọng tiếng Việt bất kỳ
                const anyVietnamese = voices.find(voice => 
                    voice.name.toLowerCase().includes('vietnamese')
                );
                this.currentVoice = anyVietnamese ? anyVietnamese.name : 'Vietnamese Female';
            }
        };
    }
    
    setupWebSpeechAPI() {
        console.log('Fallback về Web Speech API');
        this.ttsEngine = 'webspeech';
        
        if ('speechSynthesis' in window) {
            // Đợi voices load
            const loadVoices = () => {
                const voices = speechSynthesis.getVoices();
                const vietnameseVoice = voices.find(voice => 
                    voice.lang.includes('vi') && 
                    (voice.name.toLowerCase().includes('female') || 
                     voice.name.toLowerCase().includes('nữ'))
                );
                
                if (vietnameseVoice) {
                    this.currentVoice = vietnameseVoice;
                    console.log('Đã chọn giọng Web Speech:', vietnameseVoice.name);
                }
            };
            
            if (speechSynthesis.getVoices().length > 0) {
                loadVoices();
            } else {
                speechSynthesis.onvoiceschanged = loadVoices;
            }
        }
    }
    
    speak(text) {
        if (!this.isEnabled || !text) return;
        
        console.log('Đọc:', text);
        
        if (this.ttsEngine === 'responsivevoice' && typeof responsiveVoice !== 'undefined') {
            this.speakWithResponsiveVoice(text);
        } else {
            this.speakWithWebSpeech(text);
        }
    }
    
    speakWithResponsiveVoice(text) {
        try {
            responsiveVoice.cancel(); // Dừng speech hiện tại
            
            const options = {
                rate: this.rate,
                pitch: this.pitch,
                volume: this.volume,
                onstart: () => console.log('Bắt đầu đọc'),
                onend: () => console.log('Kết thúc đọc'),
                onerror: (error) => {
                    console.error('Lỗi ResponsiveVoice:', error);
                    this.speakWithWebSpeech(text); // Fallback
                }
            };
            
            responsiveVoice.speak(text, this.currentVoice || 'Vietnamese Female', options);
        } catch (error) {
            console.error('Lỗi ResponsiveVoice:', error);
            this.speakWithWebSpeech(text);
        }
    }
    
    speakWithWebSpeech(text) {
        if (!('speechSynthesis' in window)) {
            console.warn('Trình duyệt không hỗ trợ Text-to-Speech');
            return;
        }
        
        try {
            speechSynthesis.cancel();
            
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'vi-VN';
            utterance.rate = this.rate;
            utterance.pitch = this.pitch;
            utterance.volume = this.volume;
            
            if (this.currentVoice && typeof this.currentVoice === 'object') {
                utterance.voice = this.currentVoice;
            }
            
            utterance.onerror = (error) => {
                console.error('Lỗi Web Speech:', error);
            };
            
            speechSynthesis.speak(utterance);
        } catch (error) {
            console.error('Lỗi Web Speech API:', error);
        }
    }
    
    speakCorrectFeedback() {
        const messages = this.fallbackMessages.correct;
        const randomMessage = messages[Math.floor(Math.random() * messages.length)];
        this.speak(randomMessage);
    }
    
    speakIncorrectFeedback(correctAnswer) {
        const messages = [
            `Chưa đúng! Đáp án là ${correctAnswer}. Bé sẽ làm được thôi!`,
            `Sai rồi bé ơi! Đáp án đúng là ${correctAnswer}. Lần sau bé sẽ làm được!`,
            `Chưa chính xác! Đáp án là ${correctAnswer}. Bé cố gắng lên!`,
            `Gần đúng rồi! Đáp án là ${correctAnswer}. Bé thử lại nhé!`
        ];
        
        const randomMessage = messages[Math.floor(Math.random() * messages.length)];
        this.speak(randomMessage);
    }
    
    speakCustomMessage(message) {
        this.speak(message);
    }
    
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
    }
    
    setRate(rate) {
        this.rate = Math.max(0.1, Math.min(2, rate));
    }
    
    setPitch(pitch) {
        this.pitch = Math.max(0, Math.min(2, pitch));
    }
    
    toggle() {
        this.isEnabled = !this.isEnabled;
        
        if (!this.isEnabled) {
            this.stop();
        }
        
        return this.isEnabled;
    }
    
    stop() {
        if (this.ttsEngine === 'responsivevoice' && typeof responsiveVoice !== 'undefined') {
            responsiveVoice.cancel();
        }
        
        if ('speechSynthesis' in window) {
            speechSynthesis.cancel();
        }
    }
    
    // Test function để kiểm tra giọng nói
    test() {
        this.speak('Xin chào! Tôi là trợ lý học toán của bé. Chúng ta cùng học toán vui nhé!');
    }
}

// Export để sử dụng trong file khác
window.TTSManager = TTSManager;
