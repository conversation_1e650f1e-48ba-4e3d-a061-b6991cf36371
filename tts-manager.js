class TTSManager {
    constructor() {
        this.isEnabled = true;
        this.volume = 0.8;
        this.rate = 0.7; // Tốc độ đọc chậm cho trẻ em
        this.pitch = 1.2; // <PERSON> hơn một chút

        // API Configuration
        this.apiUrl = 'https://api.gpt.ge/v1/audio/speech';
        this.apiKey = 'sk-ig2AGaCPwwQG5Pyl43Ce07Ef1cE24e70A0D5706e214f32E9';
        this.voice = 'nova'; // Giọng nữ
        this.model = 'tts-1';

        // Audio context for playback
        this.audioContext = null;
        this.currentAudio = null;

        this.currentVoice = null;
        this.initializeVoice();
        
        // Fallback messages nếu không có internet
        this.fallbackMessages = {
            correct: [
                'Tuyệt vời! Bé làm đúng rồi!',
                'Giỏi quá! Bé thật thông minh!',
                'Chính xác! Bé làm rất tốt!',
                '<PERSON><PERSON>t sắc! Bé là nhà toán học nhí!'
            ],
            incorrect: [
                'Chưa đúng! Bé sẽ làm được thôi!',
                '<PERSON> rồi bé ơi! Lần sau bé sẽ làm được!',
                'Chưa chính xác! Bé cố gắng lên!',
                'Gần đúng rồi! Bé thử lại nhé!'
            ]
        };
    }
    
    initializeVoice() {
        // Khởi tạo Audio Context
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.ttsEngine = 'api';
            console.log('Sử dụng API TTS');
        } catch (error) {
            console.warn('Không thể khởi tạo AudioContext:', error);
            this.setupFallback();
        }
    }

    setupFallback() {
        // Fallback về ResponsiveVoice hoặc Web Speech API
        if (typeof responsiveVoice !== 'undefined') {
            this.setupResponsiveVoice();
        } else {
            this.setupWebSpeechAPI();
        }
    }
    
    setupResponsiveVoice() {
        console.log('Sử dụng ResponsiveVoice API');
        this.ttsEngine = 'responsivevoice';
        
        // Kiểm tra giọng nữ tiếng Việt có sẵn
        responsiveVoice.OnVoiceReady = () => {
            const voices = responsiveVoice.getVoices();
            console.log('Danh sách giọng có sẵn:', voices);
            
            // Tìm giọng nữ tiếng Việt
            const vietnameseVoice = voices.find(voice => 
                voice.name.toLowerCase().includes('vietnamese') && 
                voice.name.toLowerCase().includes('female')
            );
            
            if (vietnameseVoice) {
                this.currentVoice = vietnameseVoice.name;
                console.log('Đã chọn giọng:', this.currentVoice);
            } else {
                // Fallback về giọng tiếng Việt bất kỳ
                const anyVietnamese = voices.find(voice => 
                    voice.name.toLowerCase().includes('vietnamese')
                );
                this.currentVoice = anyVietnamese ? anyVietnamese.name : 'Vietnamese Female';
            }
        };
    }
    
    setupWebSpeechAPI() {
        console.log('Fallback về Web Speech API');
        this.ttsEngine = 'webspeech';
        
        if ('speechSynthesis' in window) {
            // Đợi voices load
            const loadVoices = () => {
                const voices = speechSynthesis.getVoices();
                const vietnameseVoice = voices.find(voice => 
                    voice.lang.includes('vi') && 
                    (voice.name.toLowerCase().includes('female') || 
                     voice.name.toLowerCase().includes('nữ'))
                );
                
                if (vietnameseVoice) {
                    this.currentVoice = vietnameseVoice;
                    console.log('Đã chọn giọng Web Speech:', vietnameseVoice.name);
                }
            };
            
            if (speechSynthesis.getVoices().length > 0) {
                loadVoices();
            } else {
                speechSynthesis.onvoiceschanged = loadVoices;
            }
        }
    }
    
    speak(text) {
        if (!this.isEnabled || !text) return;

        console.log('Đọc:', text);

        // Dừng audio hiện tại nếu có
        this.stop();

        if (this.ttsEngine === 'api') {
            this.speakWithAPI(text);
        } else if (this.ttsEngine === 'responsivevoice' && typeof responsiveVoice !== 'undefined') {
            this.speakWithResponsiveVoice(text);
        } else {
            this.speakWithWebSpeech(text);
        }
    }

    async speakWithAPI(text) {
        try {
            console.log('Gọi API TTS...');
            this.showLoadingStatus();

            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.model,
                    input: text,
                    voice: this.voice
                })
            });

            if (!response.ok) {
                throw new Error(`API Error: ${response.status} ${response.statusText}`);
            }

            const audioBlob = await response.blob();
            const audioUrl = URL.createObjectURL(audioBlob);

            // Phát audio
            this.currentAudio = new Audio(audioUrl);
            this.currentAudio.volume = this.volume;

            this.currentAudio.onended = () => {
                URL.revokeObjectURL(audioUrl);
                this.currentAudio = null;
                this.hideLoadingStatus();
                console.log('Kết thúc phát audio');
            };

            this.currentAudio.onerror = (error) => {
                console.error('Lỗi phát audio:', error);
                URL.revokeObjectURL(audioUrl);
                this.currentAudio = null;
                this.hideLoadingStatus();
                // Fallback
                this.speakWithFallback(text);
            };

            await this.currentAudio.play();
            console.log('Đang phát audio từ API');

        } catch (error) {
            console.error('Lỗi API TTS:', error);
            this.hideLoadingStatus();
            // Fallback về phương thức khác
            this.speakWithFallback(text);
        }
    }

    speakWithFallback(text) {
        if (this.ttsEngine !== 'api') return;

        console.log('Chuyển sang fallback...');
        if (typeof responsiveVoice !== 'undefined') {
            this.speakWithResponsiveVoice(text);
        } else {
            this.speakWithWebSpeech(text);
        }
    }
    
    speakWithResponsiveVoice(text) {
        try {
            responsiveVoice.cancel(); // Dừng speech hiện tại
            
            const options = {
                rate: this.rate,
                pitch: this.pitch,
                volume: this.volume,
                onstart: () => console.log('Bắt đầu đọc'),
                onend: () => console.log('Kết thúc đọc'),
                onerror: (error) => {
                    console.error('Lỗi ResponsiveVoice:', error);
                    this.speakWithWebSpeech(text); // Fallback
                }
            };
            
            responsiveVoice.speak(text, this.currentVoice || 'Vietnamese Female', options);
        } catch (error) {
            console.error('Lỗi ResponsiveVoice:', error);
            this.speakWithWebSpeech(text);
        }
    }
    
    speakWithWebSpeech(text) {
        if (!('speechSynthesis' in window)) {
            console.warn('Trình duyệt không hỗ trợ Text-to-Speech');
            return;
        }
        
        try {
            speechSynthesis.cancel();
            
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'vi-VN';
            utterance.rate = this.rate;
            utterance.pitch = this.pitch;
            utterance.volume = this.volume;
            
            if (this.currentVoice && typeof this.currentVoice === 'object') {
                utterance.voice = this.currentVoice;
            }
            
            utterance.onerror = (error) => {
                console.error('Lỗi Web Speech:', error);
            };
            
            speechSynthesis.speak(utterance);
        } catch (error) {
            console.error('Lỗi Web Speech API:', error);
        }
    }
    
    speakCorrectFeedback() {
        const messages = this.fallbackMessages.correct;
        const randomMessage = messages[Math.floor(Math.random() * messages.length)];
        this.speak(randomMessage);
    }
    
    speakIncorrectFeedback(correctAnswer) {
        const messages = [
            `Chưa đúng! Đáp án là ${correctAnswer}. Bé sẽ làm được thôi!`,
            `Sai rồi bé ơi! Đáp án đúng là ${correctAnswer}. Lần sau bé sẽ làm được!`,
            `Chưa chính xác! Đáp án là ${correctAnswer}. Bé cố gắng lên!`,
            `Gần đúng rồi! Đáp án là ${correctAnswer}. Bé thử lại nhé!`
        ];
        
        const randomMessage = messages[Math.floor(Math.random() * messages.length)];
        this.speak(randomMessage);
    }
    
    speakCustomMessage(message) {
        this.speak(message);
    }
    
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
    }
    
    setRate(rate) {
        this.rate = Math.max(0.1, Math.min(2, rate));
    }
    
    setPitch(pitch) {
        this.pitch = Math.max(0, Math.min(2, pitch));
    }
    
    toggle() {
        this.isEnabled = !this.isEnabled;
        
        if (!this.isEnabled) {
            this.stop();
        }
        
        return this.isEnabled;
    }
    
    stop() {
        // Dừng API audio
        if (this.currentAudio) {
            this.currentAudio.pause();
            this.currentAudio.currentTime = 0;
            this.currentAudio = null;
        }

        // Dừng ResponsiveVoice
        if (this.ttsEngine === 'responsivevoice' && typeof responsiveVoice !== 'undefined') {
            responsiveVoice.cancel();
        }

        // Dừng Web Speech API
        if ('speechSynthesis' in window) {
            speechSynthesis.cancel();
        }
    }
    
    // Test function để kiểm tra giọng nói
    test() {
        this.speak('Xin chào! Tôi là trợ lý học toán của bé. Chúng ta cùng học toán vui nhé!');
    }

    // Kiểm tra trạng thái API
    async checkAPIStatus() {
        try {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.model,
                    input: 'test',
                    voice: this.voice
                })
            });

            return response.ok;
        } catch (error) {
            console.error('API không khả dụng:', error);
            return false;
        }
    }

    // Lấy thông tin engine hiện tại
    getCurrentEngine() {
        if (this.ttsEngine === 'api') {
            return 'API TTS - Giọng nữ chất lượng cao';
        } else if (this.ttsEngine === 'responsivevoice') {
            return 'ResponsiveVoice - Giọng nữ Việt Nam';
        } else {
            return 'Web Speech API - Giọng hệ thống';
        }
    }

    // Hiển thị trạng thái loading
    showLoadingStatus() {
        const statusElement = document.getElementById('ttsStatus');
        if (statusElement) {
            statusElement.innerHTML = '<i class="fas fa-spinner"></i><span>Đang tạo giọng nói...</span>';
            statusElement.className = 'tts-status loading';
        }
    }

    // Ẩn trạng thái loading
    hideLoadingStatus() {
        const statusElement = document.getElementById('ttsStatus');
        if (statusElement) {
            const engineInfo = this.getCurrentEngine();
            let icon = 'fas fa-microphone';

            if (this.ttsEngine === 'api') {
                icon = 'fas fa-star';
            } else if (this.ttsEngine === 'responsivevoice') {
                icon = 'fas fa-microphone';
            } else {
                icon = 'fas fa-volume-up';
            }

            statusElement.innerHTML = `<i class="${icon}"></i><span>${engineInfo}</span>`;
            statusElement.className = 'tts-status ready';
        }
    }
}

// Export để sử dụng trong file khác
window.TTSManager = TTSManager;
