class MathGame {
    constructor() {
        this.currentOperation = 'add';
        this.correctCount = 0;
        this.totalCount = 0;
        this.currentProblem = null;

        // Khởi tạo AudioManager
        this.audioManager = new AudioManager();

        this.initializeElements();
        this.bindEvents();
        this.generateNewProblem();
    }

    initializeElements() {
        this.operationBtns = document.querySelectorAll('.operation-btn');
        this.firstNumber = document.getElementById('firstNumber');
        this.operator = document.getElementById('operator');
        this.answerInput = document.getElementById('answerInput');
        this.result = document.getElementById('result');
        this.checkBtn = document.getElementById('checkBtn');
        this.newProblemBtn = document.getElementById('newProblemBtn');
        this.feedback = document.getElementById('feedback');
        this.feedbackContent = this.feedback.querySelector('.feedback-content');
        this.feedbackIcon = this.feedback.querySelector('.feedback-icon');
        this.feedbackText = this.feedback.querySelector('.feedback-text');
        this.correctCountEl = document.getElementById('correctCount');
        this.totalCountEl = document.getElementById('totalCount');
        this.celebration = document.getElementById('celebration');

        // Audio controls
        this.audioToggleBtn = document.getElementById('audioToggleBtn');
        this.volumeSlider = document.getElementById('volumeSlider');
    }

    bindEvents() {
        // Chọn phép toán
        this.operationBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                this.audioManager.playClickSound();
                this.operationBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentOperation = btn.dataset.operation;
                this.generateNewProblem();
            });
        });

        // Kiểm tra đáp án
        this.checkBtn.addEventListener('click', () => {
            this.audioManager.playClickSound();
            this.checkAnswer();
        });

        // Tạo bài mới
        this.newProblemBtn.addEventListener('click', () => {
            this.audioManager.playClickSound();
            this.generateNewProblem();
        });

        // Enter để kiểm tra
        this.answerInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.checkAnswer();
            }
        });

        // Audio controls
        this.audioToggleBtn.addEventListener('click', () => {
            const isEnabled = this.audioManager.toggleAudio();
            const icon = this.audioToggleBtn.querySelector('i');
            if (isEnabled) {
                icon.className = 'fas fa-volume-up';
                this.audioToggleBtn.classList.remove('muted');
            } else {
                icon.className = 'fas fa-volume-mute';
                this.audioToggleBtn.classList.add('muted');
            }
        });

        this.volumeSlider.addEventListener('input', (e) => {
            const volume = e.target.value / 100;
            this.audioManager.setVolume(volume);
        });

        // Focus vào input khi tải trang
        this.answerInput.focus();
    }



    generateNewProblem() {
        this.hideFeedback();
        this.answerInput.value = '';
        this.answerInput.focus();

        if (this.currentOperation === 'add') {
            // Phép cộng: a + ? = c (với a, c <= 10 và ? >= 0)
            const result = Math.floor(Math.random() * 9) + 2; // 2-10
            const firstNum = Math.floor(Math.random() * result) + 1; // 1 đến result-1
            const answer = result - firstNum;

            this.currentProblem = {
                firstNumber: firstNum,
                operator: '+',
                answer: answer,
                result: result
            };

            this.operator.textContent = '+';
        } else {
            // Phép trừ: a - ? = c (với a <= 10, c >= 0 và ? >= 0)
            const firstNum = Math.floor(Math.random() * 9) + 2; // 2-10
            const result = Math.floor(Math.random() * firstNum); // 0 đến firstNum-1
            const answer = firstNum - result;

            this.currentProblem = {
                firstNumber: firstNum,
                operator: '-',
                answer: answer,
                result: result
            };

            this.operator.textContent = '-';
        }

        // Cập nhật hiển thị
        this.firstNumber.textContent = this.currentProblem.firstNumber;
        this.result.textContent = this.currentProblem.result;
        
        // Đọc đề bài
        this.readProblem();
    }

    readProblem() {
        // Sử dụng AudioManager để đọc đề bài
        setTimeout(() => {
            this.audioManager.playProblem(
                this.currentOperation,
                this.currentProblem.firstNumber,
                this.currentProblem.result
            );
        }, 500);
    }

    checkAnswer() {
        const userAnswer = parseInt(this.answerInput.value);
        
        if (isNaN(userAnswer)) {
            this.showFeedback(false, 'Hãy nhập một số nhé!');
            return;
        }

        this.totalCount++;
        this.totalCountEl.textContent = this.totalCount;

        if (userAnswer === this.currentProblem.answer) {
            this.correctCount++;
            this.correctCountEl.textContent = this.correctCount;
            this.showCorrectFeedback();
            this.showCelebration();
        } else {
            this.showIncorrectFeedback();
        }
    }

    showCorrectFeedback() {
        const messages = [
            'Tuyệt vời! Bé làm đúng rồi!',
            'Giỏi quá! Bé thật thông minh!',
            'Chính xác! Bé làm rất tốt!',
            'Xuất sắc! Bé là nhà toán học nhí!'
        ];

        const message = messages[Math.floor(Math.random() * messages.length)];
        this.showFeedback(true, message);

        // Phát âm thanh khen ngợi
        this.audioManager.playCorrectFeedback();

        // Tự động tạo bài mới sau 3 giây
        setTimeout(() => {
            this.generateNewProblem();
        }, 3000);
    }

    showIncorrectFeedback() {
        const messages = [
            `Chưa đúng nhé! Đáp án là ${this.currentProblem.answer}. Bé thử lại nhé!`,
            `Sai rồi bé ơi! Đáp án đúng là ${this.currentProblem.answer}. Lần sau bé sẽ làm được!`,
            `Chưa chính xác! Đáp án là ${this.currentProblem.answer}. Bé cố gắng lên!`,
            `Gần đúng rồi! Đáp án là ${this.currentProblem.answer}. Bé sẽ làm được thôi!`
        ];

        const message = messages[Math.floor(Math.random() * messages.length)];
        this.showFeedback(false, message);

        // Phát âm thanh động viên
        this.audioManager.playIncorrectFeedback();

        // Hiển thị đáp án đúng trong input
        this.answerInput.value = this.currentProblem.answer;

        // Tự động tạo bài mới sau 4 giây
        setTimeout(() => {
            this.generateNewProblem();
        }, 4000);
    }

    showFeedback(isCorrect, message) {
        this.feedbackContent.classList.remove('show', 'correct', 'incorrect');
        
        if (isCorrect) {
            this.feedbackContent.classList.add('correct');
            this.feedbackIcon.className = 'feedback-icon fas fa-star';
        } else {
            this.feedbackContent.classList.add('incorrect');
            this.feedbackIcon.className = 'feedback-icon fas fa-heart';
        }
        
        this.feedbackText.textContent = message;
        
        // Hiển thị với animation
        setTimeout(() => {
            this.feedbackContent.classList.add('show');
        }, 100);
    }

    hideFeedback() {
        this.feedbackContent.classList.remove('show');
    }

    showCelebration() {
        this.celebration.style.display = 'block';
        
        // Ẩn celebration sau 3 giây
        setTimeout(() => {
            this.celebration.style.display = 'none';
        }, 3000);
    }
}

// Khởi tạo game khi trang web được tải
document.addEventListener('DOMContentLoaded', () => {
    new MathGame();
});

// Thêm một số hiệu ứng âm thanh đơn giản
class SoundEffects {
    constructor() {
        this.audioContext = null;
        this.initAudio();
    }

    initAudio() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.log('Web Audio API không được hỗ trợ');
        }
    }

    playCorrectSound() {
        if (!this.audioContext) return;
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.setValueAtTime(523.25, this.audioContext.currentTime); // C5
        oscillator.frequency.setValueAtTime(659.25, this.audioContext.currentTime + 0.1); // E5
        oscillator.frequency.setValueAtTime(783.99, this.audioContext.currentTime + 0.2); // G5
        
        gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.5);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.5);
    }

    playIncorrectSound() {
        if (!this.audioContext) return;
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);
        oscillator.frequency.setValueAtTime(150, this.audioContext.currentTime + 0.1);
        
        gainNode.gain.setValueAtTime(0.2, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.3);
    }
}
