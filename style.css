* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Comic Neue', cursive;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin: 0;
}

.container {
    background: white;
    border-radius: 25px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    padding: 35px;
    max-width: 650px;
    width: 100%;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3);
    border-radius: 25px 25px 0 0;
}

.container::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    pointer-events: none;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(180deg); }
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.title {
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.title i {
    color: #e74c3c;
    margin-right: 10px;
}

.subtitle {
    color: #7f8c8d;
    font-size: 1.2em;
    font-weight: 400;
}

.operation-selector {
    text-align: center;
    margin-bottom: 30px;
}

.operation-selector h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.operation-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.operation-btn {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 15px;
    font-family: inherit;
    font-size: 1.1em;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(116, 185, 255, 0.3);
}

.operation-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(116, 185, 255, 0.4);
}

.operation-btn.active {
    background: linear-gradient(135deg, #00b894, #00a085);
    box-shadow: 0 5px 15px rgba(0, 184, 148, 0.3);
}

.operation-btn i {
    margin-right: 8px;
}

.math-problem {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 25px;
    padding: 30px;
    margin-bottom: 30px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(240, 147, 251, 0.3);
    position: relative;
    overflow: hidden;
}

.math-problem::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
    pointer-events: none;
}

.problem-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
    position: relative;
    z-index: 1;
}

.math-card {
    background: white;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    min-width: 80px;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.math-card:hover {
    transform: translateY(-5px);
}

.number-card:hover {
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.operator-card:hover {
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
}

.equals-card:hover {
    box-shadow: 0 12px 35px rgba(78, 205, 196, 0.4);
}

.result-card:hover {
    box-shadow: 0 12px 35px rgba(255, 234, 167, 0.4);
}

.number-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.operator-card {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.equals-card {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(78, 205, 196, 0.3);
}

.result-card {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    color: #2c3e50;
    box-shadow: 0 8px 25px rgba(255, 234, 167, 0.3);
}

.input-card {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    position: relative;
    box-shadow: 0 8px 25px rgba(116, 185, 255, 0.3);
}

.number, .operator, .equals {
    font-size: 2.5em;
    font-weight: 700;
    margin: 0;
}

.input-label {
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8em;
    color: #667eea;
    font-weight: 600;
    white-space: nowrap;
    opacity: 0;
    transition: all 0.3s ease;
}

.input-card:hover .input-label {
    opacity: 1;
    bottom: -35px;
}

.operator {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
}

.equals {
    background: linear-gradient(135deg, #fdcb6e, #e17055);
    color: white;
}

.answer-input {
    background: white;
    border: none;
    padding: 0;
    border-radius: 15px;
    font-size: 2.5em;
    font-family: inherit;
    font-weight: 700;
    text-align: center;
    width: 100%;
    height: 100%;
    transition: all 0.3s ease;
    color: #2c3e50;
    outline: none;
}

.answer-input::placeholder {
    color: #bbb;
    font-size: 1em;
    font-weight: 700;
}

.answer-input:focus {
    background: linear-gradient(135deg, #fff, #f0f8ff);
    color: #667eea;
}

.input-card:focus-within {
    transform: scale(1.05);
    box-shadow: 0 15px 40px rgba(116, 185, 255, 0.5);
    background: linear-gradient(135deg, #0984e3 0%, #74b9ff 100%);
}

/* Loại bỏ spinner cho input number */
.answer-input::-webkit-outer-spin-button,
.answer-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.answer-input[type=number] {
    -moz-appearance: textfield;
}

.controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 30px;
}

.btn {
    background: linear-gradient(135deg, #a29bfe, #6c5ce7);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 20px;
    font-family: inherit;
    font-size: 1.1em;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(162, 155, 254, 0.3);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(162, 155, 254, 0.4);
}

.btn:active {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(162, 155, 254, 0.3);
}

.btn-check {
    background: linear-gradient(135deg, #00b894, #00a085);
    box-shadow: 0 8px 25px rgba(0, 184, 148, 0.3);
}

.btn-check:hover {
    box-shadow: 0 12px 35px rgba(0, 184, 148, 0.4);
}

.btn-new {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    box-shadow: 0 8px 25px rgba(253, 121, 168, 0.3);
}

.btn-new:hover {
    box-shadow: 0 12px 35px rgba(253, 121, 168, 0.4);
}

.btn i {
    margin-right: 8px;
}

.feedback {
    text-align: center;
    margin-bottom: 20px;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feedback-content {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: scale(0);
    transition: all 0.3s ease;
}

.feedback-content.show {
    transform: scale(1);
}

.feedback-content.correct {
    background: linear-gradient(135deg, #00b894, #00a085);
    color: white;
}

.feedback-content.incorrect {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
}

.feedback-icon {
    font-size: 2em;
    margin-bottom: 10px;
    display: block;
}

.feedback-text {
    font-size: 1.2em;
    font-weight: 700;
    margin: 0;
}

.score-board {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

.score-item {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    padding: 12px 20px;
    border-radius: 20px;
    text-align: center;
    font-weight: 700;
    color: white;
    box-shadow: 0 8px 25px rgba(255, 154, 158, 0.3);
    transition: all 0.3s ease;
    min-width: 100px;
}

.score-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 154, 158, 0.4);
}

.score-item i {
    color: white;
    margin-right: 8px;
    font-size: 1.2em;
}

/* TTS Status */
.tts-status {
    text-align: center;
    margin: 15px 0;
    padding: 8px 15px;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 15px;
    font-size: 0.9em;
    color: #2c3e50;
    font-weight: 600;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.tts-status i {
    margin-right: 8px;
    color: #74b9ff;
}

.tts-status.ready {
    background: linear-gradient(135deg, #00b894, #00a085);
    color: white;
}

.tts-status.ready i {
    color: white;
}

.tts-status.loading {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    color: white;
}

.tts-status.loading i {
    color: white;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.celebration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #ff6b6b;
    animation: confetti-fall 3s linear infinite;
}

.confetti:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    background: #4ecdc4;
}

.confetti:nth-child(2) {
    left: 30%;
    animation-delay: 0.5s;
    background: #45b7d1;
}

.confetti:nth-child(3) {
    left: 50%;
    animation-delay: 1s;
    background: #96ceb4;
}

.confetti:nth-child(4) {
    left: 70%;
    animation-delay: 1.5s;
    background: #feca57;
}

.confetti:nth-child(5) {
    left: 90%;
    animation-delay: 2s;
    background: #ff9ff3;
}

@keyframes confetti-fall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}

/* Audio Controls */
.audio-controls {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 15px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    z-index: 100;
}

.audio-toggle-btn, .audio-test-btn {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    border: none;
    padding: 10px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.audio-toggle-btn:hover, .audio-test-btn:hover {
    transform: scale(1.1);
}

.audio-toggle-btn.muted {
    background: linear-gradient(135deg, #fd79a8, #e84393);
}

.audio-test-btn {
    background: linear-gradient(135deg, #00b894, #00a085);
}

.audio-test-btn:hover {
    background: linear-gradient(135deg, #00a085, #00b894);
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2c3e50;
}

.volume-slider {
    width: 80px;
    height: 5px;
    border-radius: 5px;
    background: #ddd;
    outline: none;
    cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
    appearance: none;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: #74b9ff;
    cursor: pointer;
}

.volume-slider::-moz-range-thumb {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: #74b9ff;
    cursor: pointer;
    border: none;
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
        max-width: 95%;
    }

    .title {
        font-size: 2em;
    }

    .problem-display {
        gap: 10px;
        flex-wrap: wrap;
    }

    .math-card {
        min-width: 70px;
        min-height: 70px;
        padding: 15px;
    }

    .number, .operator, .equals {
        font-size: 2em;
    }

    .answer-input {
        font-size: 2em;
    }

    .operation-buttons {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .operation-btn {
        width: 200px;
    }

    .controls {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .btn {
        width: 150px;
    }

    .score-board {
        flex-direction: column;
        gap: 10px;
        align-items: center;
    }

    .score-item {
        width: 120px;
    }

    .audio-controls {
        top: 10px;
        right: 10px;
        padding: 8px;
        gap: 8px;
    }

    .volume-control {
        display: none;
    }

    .input-label {
        font-size: 0.7em;
        bottom: -25px;
    }

    .input-card:hover .input-label {
        bottom: -30px;
    }
}
