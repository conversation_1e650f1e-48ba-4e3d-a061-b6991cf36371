* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Comic Neue', cursive;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 30px;
    max-width: 600px;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.title {
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.title i {
    color: #e74c3c;
    margin-right: 10px;
}

.subtitle {
    color: #7f8c8d;
    font-size: 1.2em;
    font-weight: 400;
}

.operation-selector {
    text-align: center;
    margin-bottom: 30px;
}

.operation-selector h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.operation-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.operation-btn {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 15px;
    font-family: inherit;
    font-size: 1.1em;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(116, 185, 255, 0.3);
}

.operation-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(116, 185, 255, 0.4);
}

.operation-btn.active {
    background: linear-gradient(135deg, #00b894, #00a085);
    box-shadow: 0 5px 15px rgba(0, 184, 148, 0.3);
}

.operation-btn i {
    margin-right: 8px;
}

.math-problem {
    background: linear-gradient(135deg, #ffecd2, #fcb69f);
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 30px;
    text-align: center;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
}

.problem-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    font-size: 3em;
    font-weight: 700;
    color: #2c3e50;
}

.number, .operator, .equals {
    background: white;
    padding: 15px 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    min-width: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.input-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.input-hint {
    position: absolute;
    bottom: -35px;
    font-size: 0.9em;
    color: #74b9ff;
    font-weight: 600;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
    white-space: nowrap;
}

.answer-input:focus + .input-hint {
    opacity: 1;
    bottom: -40px;
    transform: translateY(-5px);
}

.operator {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
}

.equals {
    background: linear-gradient(135deg, #fdcb6e, #e17055);
    color: white;
}

.answer-input {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 4px solid #74b9ff;
    padding: 20px 25px;
    border-radius: 20px;
    font-size: 1em;
    font-family: inherit;
    font-weight: 700;
    text-align: center;
    min-width: 100px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(116, 185, 255, 0.2);
    color: #2c3e50;
    position: relative;
}

.answer-input::placeholder {
    color: #74b9ff;
    font-size: 1.2em;
    font-weight: 700;
}

.answer-input:focus {
    outline: none;
    border-color: #00b894;
    box-shadow: 0 10px 30px rgba(0, 184, 148, 0.4);
    transform: scale(1.08);
    background: linear-gradient(135deg, #ffffff, #e8f5e8);
}

.answer-input:hover {
    border-color: #0984e3;
    box-shadow: 0 8px 25px rgba(9, 132, 227, 0.3);
    transform: translateY(-2px);
}

/* Thêm hiệu ứng pulse khi focus */
.answer-input:focus::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 24px;
    background: linear-gradient(45deg, #74b9ff, #00b894);
    z-index: -1;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 0.7;
        transform: scale(1);
    }
    50% {
        opacity: 0.4;
        transform: scale(1.02);
    }
    100% {
        opacity: 0.7;
        transform: scale(1);
    }
}

.controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 30px;
}

.btn {
    background: linear-gradient(135deg, #a29bfe, #6c5ce7);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 15px;
    font-family: inherit;
    font-size: 1.1em;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(162, 155, 254, 0.3);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(162, 155, 254, 0.4);
}

.btn-check {
    background: linear-gradient(135deg, #00b894, #00a085);
    box-shadow: 0 5px 15px rgba(0, 184, 148, 0.3);
}

.btn-new {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    box-shadow: 0 5px 15px rgba(253, 121, 168, 0.3);
}

.btn i {
    margin-right: 8px;
}

.feedback {
    text-align: center;
    margin-bottom: 20px;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feedback-content {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: scale(0);
    transition: all 0.3s ease;
}

.feedback-content.show {
    transform: scale(1);
}

.feedback-content.correct {
    background: linear-gradient(135deg, #00b894, #00a085);
    color: white;
}

.feedback-content.incorrect {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
}

.feedback-icon {
    font-size: 2em;
    margin-bottom: 10px;
    display: block;
}

.feedback-text {
    font-size: 1.2em;
    font-weight: 700;
    margin: 0;
}

.score-board {
    display: flex;
    justify-content: center;
    gap: 30px;
}

.score-item {
    background: linear-gradient(135deg, #ffecd2, #fcb69f);
    padding: 15px 25px;
    border-radius: 15px;
    text-align: center;
    font-weight: 700;
    color: #2c3e50;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.score-item i {
    color: #e17055;
    margin-right: 8px;
}

.celebration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #ff6b6b;
    animation: confetti-fall 3s linear infinite;
}

.confetti:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    background: #4ecdc4;
}

.confetti:nth-child(2) {
    left: 30%;
    animation-delay: 0.5s;
    background: #45b7d1;
}

.confetti:nth-child(3) {
    left: 50%;
    animation-delay: 1s;
    background: #96ceb4;
}

.confetti:nth-child(4) {
    left: 70%;
    animation-delay: 1.5s;
    background: #feca57;
}

.confetti:nth-child(5) {
    left: 90%;
    animation-delay: 2s;
    background: #ff9ff3;
}

@keyframes confetti-fall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}

/* Audio Controls */
.audio-controls {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 15px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    z-index: 100;
}

.audio-toggle-btn {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    border: none;
    padding: 10px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.audio-toggle-btn:hover {
    transform: scale(1.1);
}

.audio-toggle-btn.muted {
    background: linear-gradient(135deg, #fd79a8, #e84393);
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2c3e50;
}

.volume-slider {
    width: 80px;
    height: 5px;
    border-radius: 5px;
    background: #ddd;
    outline: none;
    cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
    appearance: none;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: #74b9ff;
    cursor: pointer;
}

.volume-slider::-moz-range-thumb {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: #74b9ff;
    cursor: pointer;
    border: none;
}

@media (max-width: 768px) {
    .container {
        padding: 20px;
        margin: 10px;
    }

    .problem-display {
        font-size: 2em;
        gap: 10px;
        flex-wrap: wrap;
    }

    .operation-buttons {
        flex-direction: column;
        align-items: center;
    }

    .controls {
        flex-direction: column;
        align-items: center;
    }

    .score-board {
        flex-direction: column;
        gap: 15px;
    }

    .audio-controls {
        top: 10px;
        right: 10px;
        padding: 10px;
        gap: 10px;
    }

    .volume-control {
        display: none;
    }

    .input-hint {
        font-size: 0.8em;
        bottom: -30px;
    }

    .answer-input:focus + .input-hint {
        bottom: -35px;
    }

    .answer-input {
        min-width: 90px;
        padding: 18px 22px;
    }
}
