class AudioManager {
    constructor() {
        this.audioCache = {};
        this.isEnabled = true;
        this.volume = 0.7;
        
        // Danh sách các file âm thanh
        this.audioFiles = {
            // Câu khen khi làm đúng
            correct: [
                'audio/khen1.mp3', // "Tuyệt vời! Bé làm đúng rồi!"
                'audio/khen2.mp3', // "Giỏi quá! Bé thật thông minh!"
                'audio/khen3.mp3', // "Chính xác! Bé làm rất tốt!"
                'audio/khen4.mp3'  // "Xuất sắc! Bé là nhà toán học nhí!"
            ],
            
            // Câu động viên khi làm sai
            incorrect: [
                'audio/dongvien1.mp3', // "Chưa đúng nhé! Bé thử lại nhé!"
                'audio/dongvien2.mp3', // "Sai rồi bé ơi! Lần sau bé sẽ làm được!"
                'audio/dongvien3.mp3', // "Chưa chính xác! <PERSON>é cố gắng lên!"
                'audio/dongvien4.mp3'  // "Gần đúng rồi! Bé sẽ làm được thôi!"
            ],
            
            // Đọc đề bài
            readProblem: {
                add: 'audio/de_cong.mp3',     // "cộng bao nhiêu bằng"
                subtract: 'audio/de_tru.mp3'  // "trừ bao nhiêu bằng"
            },
            
            // Số từ 0-10
            numbers: {
                0: 'audio/so0.mp3',
                1: 'audio/so1.mp3',
                2: 'audio/so2.mp3',
                3: 'audio/so3.mp3',
                4: 'audio/so4.mp3',
                5: 'audio/so5.mp3',
                6: 'audio/so6.mp3',
                7: 'audio/so7.mp3',
                8: 'audio/so8.mp3',
                9: 'audio/so9.mp3',
                10: 'audio/so10.mp3'
            },
            
            // Âm thanh hiệu ứng
            effects: {
                correct: 'audio/effect_correct.mp3',
                incorrect: 'audio/effect_incorrect.mp3',
                click: 'audio/effect_click.mp3'
            }
        };
        
        this.preloadAudio();
    }
    
    preloadAudio() {
        // Preload tất cả file âm thanh
        const allFiles = [
            ...this.audioFiles.correct,
            ...this.audioFiles.incorrect,
            ...Object.values(this.audioFiles.readProblem),
            ...Object.values(this.audioFiles.numbers),
            ...Object.values(this.audioFiles.effects)
        ];
        
        allFiles.forEach(file => {
            if (file) {
                const audio = new Audio();
                audio.preload = 'auto';
                audio.volume = this.volume;
                audio.src = file;
                
                // Xử lý lỗi nếu file không tồn tại
                audio.onerror = () => {
                    console.warn(`Không thể tải file âm thanh: ${file}`);
                };
                
                this.audioCache[file] = audio;
            }
        });
    }
    
    playAudio(filePath) {
        if (!this.isEnabled || !filePath) return;
        
        const audio = this.audioCache[filePath];
        if (audio) {
            // Dừng audio hiện tại nếu đang phát
            audio.currentTime = 0;
            audio.play().catch(error => {
                console.warn(`Không thể phát âm thanh: ${filePath}`, error);
            });
        }
    }
    
    playCorrectFeedback() {
        const randomIndex = Math.floor(Math.random() * this.audioFiles.correct.length);
        const audioFile = this.audioFiles.correct[randomIndex];
        this.playAudio(audioFile);
        
        // Phát hiệu ứng âm thanh
        setTimeout(() => {
            this.playAudio(this.audioFiles.effects.correct);
        }, 100);
    }
    
    playIncorrectFeedback() {
        const randomIndex = Math.floor(Math.random() * this.audioFiles.incorrect.length);
        const audioFile = this.audioFiles.incorrect[randomIndex];
        this.playAudio(audioFile);
        
        // Phát hiệu ứng âm thanh
        setTimeout(() => {
            this.playAudio(this.audioFiles.effects.incorrect);
        }, 100);
    }
    
    playNumber(number) {
        const audioFile = this.audioFiles.numbers[number];
        if (audioFile) {
            this.playAudio(audioFile);
        }
    }
    
    playProblem(operation, firstNumber, result) {
        // Phát số đầu tiên
        this.playNumber(firstNumber);
        
        // Phát phép toán
        setTimeout(() => {
            const operationFile = this.audioFiles.readProblem[operation];
            this.playAudio(operationFile);
        }, 800);
        
        // Phát kết quả
        setTimeout(() => {
            this.playNumber(result);
        }, 1600);
    }
    
    playClickSound() {
        this.playAudio(this.audioFiles.effects.click);
    }
    
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        Object.values(this.audioCache).forEach(audio => {
            audio.volume = this.volume;
        });
    }
    
    toggleAudio() {
        this.isEnabled = !this.isEnabled;
        return this.isEnabled;
    }
    
    stopAllAudio() {
        Object.values(this.audioCache).forEach(audio => {
            audio.pause();
            audio.currentTime = 0;
        });
    }
}

// Export để sử dụng trong file khác
window.AudioManager = AudioManager;
