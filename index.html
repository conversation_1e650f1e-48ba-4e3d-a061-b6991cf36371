<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Họ<PERSON> V<PERSON> - <PERSON>ép Cộng Trừ Lớp 1</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Comic+Neue:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">
                <i class="fas fa-calculator"></i>
                Học Toán Vui Cùng Bé
            </h1>
            <p class="subtitle">Phép cộng và trừ trong phạm vi 10</p>
        </header>

        <div class="game-area">
            <!-- Chọn phép toán -->
            <div class="operation-selector">
                <h3>Chọn phép toán:</h3>
                <div class="operation-buttons">
                    <button class="operation-btn active" data-operation="add">
                        <i class="fas fa-plus"></i>
                        Phép Cộng
                    </button>
                    <button class="operation-btn" data-operation="subtract">
                        <i class="fas fa-minus"></i>
                        Phép Trừ
                    </button>
                </div>
            </div>

            <!-- Hiển thị bài toán -->
            <div class="math-problem">
                <div class="problem-display">
                    <span class="number" id="firstNumber">5</span>
                    <span class="operator" id="operator">+</span>
                    <input type="number" class="answer-input" id="answerInput" placeholder="?" min="0" max="10">
                    <span class="equals">=</span>
                    <span class="number" id="result">8</span>
                </div>
            </div>

            <!-- Nút điều khiển -->
            <div class="controls">
                <button class="btn btn-check" id="checkBtn">
                    <i class="fas fa-check"></i>
                    Kiểm tra
                </button>
                <button class="btn btn-new" id="newProblemBtn">
                    <i class="fas fa-refresh"></i>
                    Bài mới
                </button>
            </div>

            <!-- Khu vực phản hồi -->
            <div class="feedback" id="feedback">
                <div class="feedback-content">
                    <i class="feedback-icon"></i>
                    <p class="feedback-text"></p>
                </div>
            </div>

            <!-- Điểm số -->
            <div class="score-board">
                <div class="score-item">
                    <i class="fas fa-star"></i>
                    <span>Đúng: <span id="correctCount">0</span></span>
                </div>
                <div class="score-item">
                    <i class="fas fa-heart"></i>
                    <span>Tổng: <span id="totalCount">0</span></span>
                </div>
            </div>
        </div>

        <!-- Hiệu ứng khen ngợi -->
        <div class="celebration" id="celebration">
            <div class="confetti"></div>
            <div class="confetti"></div>
            <div class="confetti"></div>
            <div class="confetti"></div>
            <div class="confetti"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
