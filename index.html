<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Họ<PERSON> - <PERSON>ép Cộng Trừ Lớp 1</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Comic+Neue:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- ResponsiveVoice API -->
    <script src="https://code.responsivevoice.org/responsivevoice.js?key=FREE"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">
                <i class="fas fa-calculator"></i>
                <PERSON><PERSON><PERSON> Vui <PERSON>ùng <PERSON>
            </h1>
            <p class="subtitle">Phép cộng và trừ trong phạm vi 10</p>
        </header>

        <div class="game-area">
            <!-- Chọn phép toán -->
            <div class="operation-selector">
                <h3>Chọn phép toán:</h3>
                <div class="operation-buttons">
                    <button class="operation-btn active" data-operation="add">
                        <i class="fas fa-plus"></i>
                        Phép Cộng
                    </button>
                    <button class="operation-btn" data-operation="subtract">
                        <i class="fas fa-minus"></i>
                        Phép Trừ
                    </button>
                </div>
            </div>

            <!-- Hiển thị bài toán -->
            <div class="math-problem">
                <div class="problem-display">
                    <div class="math-card number-card">
                        <span class="number" id="firstNumber">5</span>
                    </div>
                    <div class="math-card operator-card">
                        <span class="operator" id="operator">+</span>
                    </div>
                    <div class="math-card input-card">
                        <input type="number" class="answer-input" id="answerInput" placeholder="?" min="0" max="10">
                        <div class="input-label">Nhập đáp án</div>
                    </div>
                    <div class="math-card equals-card">
                        <span class="equals">=</span>
                    </div>
                    <div class="math-card result-card">
                        <span class="number" id="result">8</span>
                    </div>
                </div>
            </div>

            <!-- Nút điều khiển -->
            <div class="controls">
                <button class="btn btn-check" id="checkBtn">
                    <i class="fas fa-check"></i>
                    Kiểm tra
                </button>
                <button class="btn btn-new" id="newProblemBtn">
                    <i class="fas fa-refresh"></i>
                    Bài mới
                </button>
            </div>

            <!-- Khu vực phản hồi -->
            <div class="feedback" id="feedback">
                <div class="feedback-content">
                    <i class="feedback-icon"></i>
                    <p class="feedback-text"></p>
                </div>
            </div>

            <!-- Thông báo trạng thái TTS -->
            <div class="tts-status" id="ttsStatus">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Đang khởi tạo giọng nói...</span>
            </div>

            <!-- Điểm số -->
            <div class="score-board">
                <div class="score-item">
                    <i class="fas fa-star"></i>
                    <span>Đúng: <span id="correctCount">0</span></span>
                </div>
                <div class="score-item">
                    <i class="fas fa-heart"></i>
                    <span>Tổng: <span id="totalCount">0</span></span>
                </div>
            </div>
        </div>

        <!-- Hiệu ứng khen ngợi -->
        <div class="celebration" id="celebration">
            <div class="confetti"></div>
            <div class="confetti"></div>
            <div class="confetti"></div>
            <div class="confetti"></div>
            <div class="confetti"></div>
        </div>
    </div>

    <!-- Thêm nút điều khiển âm thanh -->
    <div class="audio-controls">
        <button class="audio-toggle-btn" id="audioToggleBtn" title="Bật/Tắt âm thanh">
            <i class="fas fa-volume-up"></i>
        </button>
        <button class="audio-test-btn" id="audioTestBtn" title="Test giọng nói">
            <i class="fas fa-play"></i>
        </button>
        <div class="volume-control">
            <i class="fas fa-volume-down"></i>
            <input type="range" class="volume-slider" id="volumeSlider" min="0" max="100" value="80">
            <i class="fas fa-volume-up"></i>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
